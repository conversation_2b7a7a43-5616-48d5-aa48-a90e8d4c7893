CREATE OR REPLACE FUNCTION public.tms_hlpr_handle_bulk_booking(
    srvc_req_id_ bigint,
    form_data_ jsonb,
    org_id_ integer
)
RETURNS json
LANGUAGE plpgsql
AS $function$
declare
    status boolean default false;
    message text default 'Internal error';
    booking_mode text;
    capacity_id_ bigint;
    booking_details_ jsonb;
    selected_week_data jsonb;
    selected_slot_data jsonb;
    capacity_resp json;
    update_resp json;
begin
    -- Check if this is a bulk booking request
    if not (form_data_ ? 'bulk_booking_mode') then
        return json_build_object('status', true, 'message', 'No bulk booking data');
    end if;
    
    booking_mode = form_data_->>'bulk_booking_mode';
    
    -- Handle different booking modes
    if booking_mode = 'auto_book' then
        -- For auto booking, we could implement automatic capacity selection
        -- For now, just mark as successful without specific booking
        status = true;
        message = 'Auto booking mode - capacity will be assigned automatically';
        
    elsif booking_mode = 'select_fulfillment_day' then
        -- Handle week-based booking
        selected_week_data = form_data_->'selected_week_data';
        
        if selected_week_data is not null then
            -- Create booking details for the selected week
            booking_details_ = json_build_object(
                'booking_mode', 'fulfillment_week',
                'selected_week', selected_week_data->'selectedWeek',
                'week_start', selected_week_data->'selectedWeek'->>'start',
                'week_end', selected_week_data->'selectedWeek'->>'end'
            )::jsonb;
            
            -- Update the service request with week booking details
            update_resp = tms_hlpr_update_srvc_req_booking_data(srvc_req_id_, null, booking_details_);
            
            if (update_resp->>'status')::boolean then
                status = true;
                message = 'Week booking data saved successfully';
            else
                message = 'Failed to save week booking data: ' || (update_resp->>'message');
            end if;
        else
            message = 'Selected week data is missing';
        end if;
        
    elsif booking_mode = 'select_specific_slot' then
        -- Handle specific slot booking
        selected_slot_data = form_data_->'selected_slot_data';
        
        if selected_slot_data is not null then
            -- Create booking details for the specific slot
            booking_details_ = json_build_object(
                'booking_mode', 'specific_slot',
                'selected_day', selected_slot_data->'selectedDay',
                'selected_slot', selected_slot_data->'selectedSlot',
                'booking_date', selected_slot_data->'selectedDay'->>'date',
                'slot_label', selected_slot_data->'selectedSlot'->>'label'
            )::jsonb;
            
            -- Try to get capacity_id if available
            -- This would require integration with the capacity system
            -- For now, we'll save the booking details without capacity_id
            
            -- Update the service request with slot booking details
            update_resp = tms_hlpr_update_srvc_req_booking_data(srvc_req_id_, capacity_id_, booking_details_);
            
            if (update_resp->>'status')::boolean then
                status = true;
                message = 'Specific slot booking data saved successfully';
            else
                message = 'Failed to save slot booking data: ' || (update_resp->>'message');
            end if;
        else
            message = 'Selected slot data is missing';
        end if;
        
    else
        message = 'Unknown booking mode: ' || booking_mode;
    end if;
    
    return json_build_object(
        'status', status,
        'message', message,
        'booking_mode', booking_mode
    );
    
exception when others then
    return json_build_object(
        'status', false,
        'message', 'Error in bulk booking handler: ' || SQLERRM
    );
end;
$function$;
